#!/usr/bin/env python3
"""
Policy Gradient module
"""

import numpy as np


def policy(matrix, weight):
    """
    Computes the policy with a weight of a matrix.
    
    Args:
        matrix: numpy.ndarray of shape (m, n) representing the state matrix
        weight: numpy.ndarray of shape (n, k) representing the weight matrix
    
    Returns:
        numpy.ndarray of shape (m, k) representing the policy probabilities
    """
    # Compute the dot product of matrix and weight
    z = np.dot(matrix, weight)
    
    # Apply softmax to get probabilities
    # Subtract max for numerical stability
    z_max = np.max(z, axis=1, keepdims=True)
    exp_z = np.exp(z - z_max)
    softmax = exp_z / np.sum(exp_z, axis=1, keepdims=True)
    
    return softmax


def policy_gradient(state, weight):
    """
    Computes the Monte-Carlo policy gradient based on a state and a weight matrix.

    Args:
        state: numpy.ndarray representing the current observation of the environment
        weight: numpy.ndarray matrix of random weight

    Returns:
        tuple: (action, gradient) where:
            - action: int, the selected action
            - gradient: numpy.ndarray, the policy gradient
    """
    # Reshape state to be a row vector if it's 1D
    if state.ndim == 1:
        state = state.reshape(1, -1)

    # Get action probabilities using the policy function
    probs = policy(state, weight)
    probs = probs.flatten()  # Convert to 1D array

    # Sample action from the probability distribution
    action = np.random.choice(len(probs), p=probs)

    # Compute the gradient of log probability
    # For softmax policy gradient: grad = state.T @ (one_hot - probs)
    one_hot = np.zeros_like(probs)
    one_hot[action] = 1

    # Gradient computation: outer product of state and (one_hot - probs)
    gradient = np.outer(state.flatten(), one_hot - probs)

    return action, gradient
