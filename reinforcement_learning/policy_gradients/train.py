#!/usr/bin/env python3
"""
Training module for policy gradient
"""

import numpy as np
policy_gradient = __import__('policy_gradient').policy_gradient


def train(env, nb_episodes, alpha=0.000045, gamma=0.98):
    """
    Implements a full training using policy gradients.
    
    Args:
        env: initial environment
        nb_episodes: number of episodes used for training
        alpha: the learning rate
        gamma: the discount factor
    
    Returns:
        list: all values of the score (sum of all rewards during one episode loop)
    """
    # Get environment dimensions
    state, _ = env.reset()
    state_dim = len(state)
    action_dim = env.action_space.n
    
    # Initialize weight matrix randomly
    weight = np.random.rand(state_dim, action_dim)
    
    scores = []
    
    for episode in range(nb_episodes):
        # Reset environment for new episode
        state, _ = env.reset()
        
        # Store episode data
        states = []
        actions = []
        rewards = []
        gradients = []
        
        # Run one episode
        done = False
        while not done:
            # Get action and gradient from policy
            action, gradient = policy_gradient(state, weight)
            
            # Store current state and gradient
            states.append(state.copy())
            gradients.append(gradient)
            actions.append(action)
            
            # Take action in environment
            state, reward, terminated, truncated, _ = env.step(action)
            rewards.append(reward)
            
            # Check if episode is done
            done = terminated or truncated
        
        # Calculate episode score
        score = sum(rewards)
        scores.append(score)
        
        # Print episode info
        print(f"Episode: {episode} Score: {score}")
        
        # Calculate discounted rewards (returns)
        returns = []
        G = 0
        for reward in reversed(rewards):
            G = reward + gamma * G
            returns.insert(0, G)
        
        # Convert to numpy array and normalize
        returns = np.array(returns)
        if len(returns) > 1:
            returns = (returns - np.mean(returns)) / (np.std(returns) + 1e-8)
        
        # Update weights using policy gradient
        for i in range(len(gradients)):
            weight += alpha * returns[i] * gradients[i]
    
    return scores
